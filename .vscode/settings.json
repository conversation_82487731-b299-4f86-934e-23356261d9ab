{
  // Formatter settings
  "[typescript]": {
    "editor.defaultFormatter": "vscode.typescript-language-features",
    "editor.formatOnSave": true,
    "editor.codeActionsOnSave": {
      "source.fixAll.eslint": "explicit"
    }
  },
  "[javascript]": {
    "editor.defaultFormatter": "vscode.typescript-language-features",
    "editor.formatOnSave": true,
    "editor.codeActionsOnSave": {
      "source.fixAll.eslint": "explicit"
    }
  },
  "[json]": {
    "editor.defaultFormatter": "vscode.json-language-features"
  },
  "[html]": {
    "editor.defaultFormatter": "vscode.html-language-features"
  },
  "[scss]": {
    "editor.defaultFormatter": "vscode.css-language-features"
  },
  "[css]": {
    "editor.defaultFormatter": "vscode.css-language-features"
  },

  // Editor settings based on TSLint rules
  "editor.tabSize": 2,
  "editor.insertSpaces": true,
  "editor.detectIndentation": false,
  "editor.trimAutoWhitespace": true,
  "files.trimTrailingWhitespace": true,
  "files.insertFinalNewline": true,
  "editor.rulers": [140],

  // TypeScript specific settings
  "typescript.preferences.quoteStyle": "single",
  "javascript.preferences.quoteStyle": "single",
  "typescript.format.semicolons": "insert",
  "javascript.format.semicolons": "insert",

  // ESLint settings
  "eslint.validate": [
    "javascript",
    "typescript"
  ],
  "eslint.format.enable": false,

  // Angular specific settings
  "emmet.includeLanguages": {
    "typescript": "html"
  }
}
